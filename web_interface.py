#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相机抓拍系统Web界面
"""

import asyncio
import json
import base64
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

from fastapi import FastAPI, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from camera_capture_system import CameraCaptureSystem, CaptureResult
from config import ConfigManager, CameraConfig
from utils import ImageProcessor, FileManager, format_timestamp

# 创建FastAPI应用
app = FastAPI(
    title="相机抓拍系统",
    description="基于日志分析的相机抓拍系统Web接口",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
config_manager = ConfigManager()
capture_system = None
websocket_connections: List[WebSocket] = []

# Pydantic模型
class CaptureRequest(BaseModel):
    count: int = 1
    interval: float = 1.0
    trigger_type: str = "IA_TRIGGER_FORMAL"

class ConfigUpdateRequest(BaseModel):
    key: str
    value: str
    config_type: str = "camera"  # camera 或 system

class CaptureResponse(BaseModel):
    success: bool
    message: str
    results: List[Dict[str, Any]] = []
    statistics: Dict[str, Any] = {}

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def send_message(self, message: dict, websocket: WebSocket):
        try:
            await websocket.send_text(json.dumps(message))
        except:
            self.disconnect(websocket)

    async def broadcast(self, message: dict):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except:
                disconnected.append(connection)
        
        for connection in disconnected:
            self.disconnect(connection)

manager = ConnectionManager()

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    global capture_system
    
    # 加载配置
    config_manager.load_config()
    
    # 创建必要的目录
    camera_config = config_manager.get_camera_config()
    FileManager.ensure_directory(camera_config.save_directory)
    FileManager.ensure_directory("static")
    
    print("Web服务器启动完成")

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    global capture_system
    if capture_system:
        await capture_system.cleanup()
    print("Web服务器关闭完成")

# API路由
@app.get("/", response_class=HTMLResponse)
async def read_root():
    """主页"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>相机抓拍系统</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { max-width: 1200px; margin: 0 auto; }
            .card { border: 1px solid #ddd; padding: 20px; margin: 10px 0; border-radius: 5px; }
            .button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; }
            .button:hover { background: #0056b3; }
            .status { padding: 5px 10px; border-radius: 3px; }
            .status.online { background: #d4edda; color: #155724; }
            .status.offline { background: #f8d7da; color: #721c24; }
            .log { background: #f8f9fa; padding: 10px; border-radius: 3px; height: 200px; overflow-y: auto; }
            .form-group { margin: 10px 0; }
            .form-group label { display: block; margin-bottom: 5px; }
            .form-group input, .form-group select { width: 200px; padding: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>相机抓拍系统</h1>
            
            <div class="card">
                <h2>系统状态</h2>
                <div id="status">检查中...</div>
                <button class="button" onclick="checkStatus()">刷新状态</button>
            </div>
            
            <div class="card">
                <h2>抓拍控制</h2>
                <div class="form-group">
                    <label>抓拍次数:</label>
                    <input type="number" id="captureCount" value="1" min="1" max="10">
                </div>
                <div class="form-group">
                    <label>间隔时间(秒):</label>
                    <input type="number" id="captureInterval" value="1" min="0.1" step="0.1">
                </div>
                <button class="button" onclick="startCapture()">开始抓拍</button>
                <button class="button" onclick="stopCapture()">停止抓拍</button>
            </div>
            
            <div class="card">
                <h2>抓拍结果</h2>
                <div id="captureResults">暂无结果</div>
            </div>
            
            <div class="card">
                <h2>实时日志</h2>
                <div id="logContainer" class="log"></div>
            </div>
        </div>
        
        <script>
            let ws = null;
            let capturing = false;
            
            // 连接WebSocket
            function connectWebSocket() {
                ws = new WebSocket(`ws://${window.location.host}/ws`);
                
                ws.onopen = function(event) {
                    addLog("WebSocket连接已建立");
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };
                
                ws.onclose = function(event) {
                    addLog("WebSocket连接已关闭");
                    setTimeout(connectWebSocket, 3000);
                };
                
                ws.onerror = function(error) {
                    addLog("WebSocket错误: " + error);
                };
            }
            
            // 处理WebSocket消息
            function handleWebSocketMessage(data) {
                if (data.type === 'log') {
                    addLog(data.message);
                } else if (data.type === 'capture_result') {
                    updateCaptureResults(data.result);
                } else if (data.type === 'status_update') {
                    updateStatus(data.status);
                }
            }
            
            // 添加日志
            function addLog(message) {
                const logContainer = document.getElementById('logContainer');
                const timestamp = new Date().toLocaleTimeString();
                logContainer.innerHTML += `[${timestamp}] ${message}<br>`;
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            
            // 检查状态
            async function checkStatus() {
                try {
                    const response = await fetch('/api/status');
                    const data = await response.json();
                    updateStatus(data);
                } catch (error) {
                    document.getElementById('status').innerHTML = 
                        '<span class="status offline">连接失败</span>';
                }
            }
            
            // 更新状态显示
            function updateStatus(data) {
                const statusElement = document.getElementById('status');
                const statusClass = data.camera_online ? 'online' : 'offline';
                const statusText = data.camera_online ? '在线' : '离线';
                
                statusElement.innerHTML = `
                    <span class="status ${statusClass}">相机: ${statusText}</span>
                    <span class="status ${data.network_ok ? 'online' : 'offline'}">网络: ${data.network_ok ? '正常' : '异常'}</span>
                    <br>存储空间: ${data.disk_usage.free_gb.toFixed(2)}GB 可用
                `;
            }
            
            // 开始抓拍
            async function startCapture() {
                if (capturing) return;
                
                capturing = true;
                const count = parseInt(document.getElementById('captureCount').value);
                const interval = parseFloat(document.getElementById('captureInterval').value);
                
                try {
                    const response = await fetch('/api/capture', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            count: count,
                            interval: interval
                        })
                    });
                    
                    const data = await response.json();
                    addLog(`抓拍请求已发送: ${data.message}`);
                    
                } catch (error) {
                    addLog(`抓拍请求失败: ${error}`);
                } finally {
                    capturing = false;
                }
            }
            
            // 停止抓拍
            function stopCapture() {
                capturing = false;
                addLog("抓拍已停止");
            }
            
            // 更新抓拍结果
            function updateCaptureResults(result) {
                const resultsElement = document.getElementById('captureResults');
                const timestamp = new Date(result.timestamp).toLocaleString();
                const status = result.success ? '成功' : '失败';
                const statusClass = result.success ? 'online' : 'offline';
                
                resultsElement.innerHTML += `
                    <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 3px;">
                        <span class="status ${statusClass}">${status}</span>
                        <strong>时间:</strong> ${timestamp}<br>
                        ${result.success ? `<strong>文件:</strong> ${result.file_path}<br>` : ''}
                        ${result.plate_info ? `<strong>车牌:</strong> ${result.plate_info.plate_number}<br>` : ''}
                        ${result.error_message ? `<strong>错误:</strong> ${result.error_message}<br>` : ''}
                    </div>
                `;
            }
            
            // 页面加载完成后初始化
            window.onload = function() {
                connectWebSocket();
                checkStatus();
            };
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/api/status")
async def get_status():
    """获取系统状态"""
    try:
        # 初始化抓拍系统（如果还没有初始化）
        global capture_system
        if not capture_system:
            camera_config = config_manager.get_camera_config()
            capture_system = CameraCaptureSystem(camera_config)
            await capture_system.initialize()
        
        # 获取相机状态
        camera_status = await capture_system.get_camera_status()
        
        # 获取磁盘使用情况
        camera_config = config_manager.get_camera_config()
        disk_usage = FileManager.get_disk_usage(camera_config.save_directory)
        
        return {
            "camera_online": camera_status["online"],
            "network_ok": True,  # 简化处理
            "disk_usage": disk_usage,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/capture")
async def trigger_capture(request: CaptureRequest, background_tasks: BackgroundTasks):
    """触发抓拍"""
    try:
        # 初始化抓拍系统（如果还没有初始化）
        global capture_system
        if not capture_system:
            camera_config = config_manager.get_camera_config()
            capture_system = CameraCaptureSystem(camera_config)
            await capture_system.initialize()
        
        # 添加后台任务执行抓拍
        background_tasks.add_task(
            execute_capture_task, 
            request.count, 
            request.interval
        )
        
        return CaptureResponse(
            success=True,
            message=f"抓拍任务已启动，将执行{request.count}次抓拍"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def execute_capture_task(count: int, interval: float):
    """执行抓拍任务"""
    global capture_system
    
    try:
        if count > 1:
            results = await capture_system.batch_capture(count, interval)
        else:
            result = await capture_system.trigger_capture()
            results = [result]
        
        # 通过WebSocket广播结果
        for result in results:
            result_data = {
                "type": "capture_result",
                "result": {
                    "success": result.success,
                    "timestamp": result.timestamp.isoformat(),
                    "file_path": result.file_path,
                    "plate_info": result.plate_info,
                    "error_message": result.error_message
                }
            }
            await manager.broadcast(result_data)
        
    except Exception as e:
        error_data = {
            "type": "log",
            "message": f"抓拍任务执行失败: {e}"
        }
        await manager.broadcast(error_data)

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点"""
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # 处理客户端消息（如果需要）
            pass
    except WebSocketDisconnect:
        manager.disconnect(websocket)

@app.get("/api/images/{filename}")
async def get_image(filename: str):
    """获取图片文件"""
    camera_config = config_manager.get_camera_config()
    file_path = Path(camera_config.save_directory) / filename
    
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="图片文件不存在")
    
    return FileResponse(file_path)

@app.get("/api/config")
async def get_config():
    """获取配置"""
    camera_config = config_manager.get_camera_config()
    system_config = config_manager.get_system_config()
    
    return {
        "camera": camera_config.__dict__,
        "system": system_config.__dict__
    }

@app.post("/api/config")
async def update_config(request: ConfigUpdateRequest):
    """更新配置"""
    try:
        if request.config_type == "camera":
            success = config_manager.update_camera_config(**{request.key: request.value})
        else:
            success = config_manager.update_system_config(**{request.key: request.value})
        
        if success:
            return {"success": True, "message": "配置更新成功"}
        else:
            raise HTTPException(status_code=400, detail="配置更新失败")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "web_interface:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
