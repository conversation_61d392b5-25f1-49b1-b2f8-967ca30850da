# 相机抓拍系统依赖包

# 核心依赖
aiohttp>=3.8.0
asyncio-mqtt>=0.11.0
fastapi>=0.95.0
uvicorn[standard]>=0.20.0
websockets>=10.4

# 图像处理
opencv-python>=4.7.0
Pillow>=9.4.0
numpy>=1.24.0

# 数据处理
pandas>=1.5.0
pydantic>=1.10.0

# 网络和HTTP
requests>=2.28.0
httpx>=0.24.0

# 配置和日志
python-dotenv>=1.0.0
loguru>=0.6.0

# 开发和测试
pytest>=7.2.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# 可选依赖（车牌识别相关）
# easyocr>=1.6.0
# paddlepaddle>=2.4.0
# paddleocr>=2.6.0

# 系统监控
psutil>=5.9.0

# 数据库（可选）
# sqlalchemy>=2.0.0
# aiosqlite>=0.19.0

# 加密和安全
cryptography>=40.0.0
