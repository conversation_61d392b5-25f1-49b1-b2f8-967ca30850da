#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相机抓拍系统 - 精简版
专注于网络通信和文件接收，无图像处理依赖
"""

import asyncio
import aiohttp
import socket
import json
import base64
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import dataclass
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class CameraConfig:
    """相机配置"""
    ip: str = "**************"
    port: int = 4080
    username: str = "admin"
    password: str = "ZZ@jtzf2526618"
    data_port: int = 10008
    client_port: int = 20002
    timeout: int = 30
    save_directory: str = "captured_images"

@dataclass
class CaptureResult:
    """抓拍结果"""
    success: bool
    timestamp: datetime
    image_data: Optional[bytes] = None
    error_message: Optional[str] = None
    file_path: Optional[str] = None
    image_size: int = 0

class CameraAuthenticator:
    """相机认证管理器"""
    
    def __init__(self, config: CameraConfig):
        self.config = config
        self.session_id: Optional[str] = None
        self.auth_token: Optional[str] = None
        
    async def authenticate(self, session: aiohttp.ClientSession) -> bool:
        """执行认证"""
        try:
            # 构建认证数据
            auth_data = {
                "UserName": self.config.username,
                "Password": self._encode_password(self.config.password),
                "AuthType": "WEB"
            }
            
            url = f"http://{self.config.ip}:{self.config.port}/LAPI/V1.0/System/Security/Login"
            
            async with session.put(url, json=auth_data) as response:
                if response.status == 200:
                    result = await response.json()
                    self.session_id = result.get("SessionID")
                    self.auth_token = result.get("Token")
                    logger.info(f"认证成功，SessionID: {self.session_id}")
                    return True
                else:
                    logger.error(f"认证失败，状态码: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"认证过程中发生错误: {e}")
            return False
    
    def _encode_password(self, password: str) -> str:
        """密码编码"""
        return base64.b64encode(password.encode()).decode()
    
    def get_auth_headers(self) -> Dict[str, str]:
        """获取认证头"""
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "CameraClient/1.0"
        }
        
        if self.session_id:
            headers["X-Session-ID"] = self.session_id
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
            
        return headers

class ImageDataReceiver:
    """图片数据接收器"""
    
    def __init__(self, config: CameraConfig):
        self.config = config
        self.server_socket: Optional[socket.socket] = None
        self.is_listening = False
        
    async def start_listening(self) -> bool:
        """开始监听图片数据"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('0.0.0.0', self.config.client_port))
            self.server_socket.listen(1)
            self.server_socket.settimeout(self.config.timeout)
            
            self.is_listening = True
            logger.info(f"开始监听端口 {self.config.client_port} 等待图片数据")
            return True
            
        except Exception as e:
            logger.error(f"启动监听失败: {e}")
            return False
    
    async def receive_image_data(self) -> Optional[bytes]:
        """接收图片数据"""
        if not self.is_listening or not self.server_socket:
            return None
            
        try:
            # 等待连接
            client_socket, addr = self.server_socket.accept()
            logger.info(f"接收到来自 {addr} 的连接")
            
            # 接收数据
            image_data = b''
            total_received = 0
            
            while True:
                chunk = client_socket.recv(8192)
                if not chunk:
                    break
                image_data += chunk
                total_received += len(chunk)
                
                # 显示接收进度
                if total_received % (100 * 1024) == 0:  # 每100KB显示一次
                    logger.info(f"已接收数据: {total_received / 1024:.1f} KB")
                
            client_socket.close()
            logger.info(f"接收完成，总大小: {len(image_data)} 字节 ({len(image_data)/1024/1024:.2f} MB)")
            return image_data
            
        except socket.timeout:
            logger.warning("接收图片数据超时")
            return None
        except Exception as e:
            logger.error(f"接收图片数据时发生错误: {e}")
            return None
    
    def stop_listening(self):
        """停止监听"""
        self.is_listening = False
        if self.server_socket:
            self.server_socket.close()
            self.server_socket = None

class MinimalCameraSystem:
    """精简版相机抓拍系统"""
    
    def __init__(self, config: CameraConfig):
        self.config = config
        self.authenticator = CameraAuthenticator(config)
        self.image_receiver = ImageDataReceiver(config)
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def initialize(self) -> bool:
        """初始化系统"""
        try:
            # 创建保存目录
            Path(self.config.save_directory).mkdir(exist_ok=True)
            
            # 创建HTTP会话
            connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            )
            
            # 执行认证
            if not await self.authenticator.authenticate(self.session):
                return False
            
            # 启动图片数据监听
            if not await self.image_receiver.start_listening():
                return False
                
            logger.info("相机抓拍系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            return False
    
    async def keep_alive(self) -> bool:
        """保持连接活跃"""
        try:
            url = f"http://{self.config.ip}:{self.config.port}/LAPI/V1.0/System/Security/KeepAlive"
            headers = self.authenticator.get_auth_headers()
            
            async with self.session.put(url, headers=headers) as response:
                if response.status == 200:
                    logger.debug("保活成功")
                    return True
                else:
                    logger.warning(f"保活失败，状态码: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"保活过程中发生错误: {e}")
            return False
    
    async def trigger_capture(self, trigger_type: str = "IA_TRIGGER_FORMAL") -> CaptureResult:
        """触发抓拍"""
        capture_time = datetime.now()
        
        try:
            # 发送抓拍指令
            url = f"http://{self.config.ip}:{self.config.port}/LAPI/V1.0/Smart/CaptureExecution"
            params = {trigger_type: ""}
            headers = self.authenticator.get_auth_headers()
            
            logger.info(f"发送抓拍指令到: {url}")
            
            async with self.session.put(url, params=params, headers=headers) as response:
                if response.status == 200:
                    logger.info("抓拍指令发送成功")
                    
                    # 接收图片数据
                    image_data = await self.image_receiver.receive_image_data()
                    
                    if image_data:
                        # 保存图片
                        file_path = await self._save_image(image_data, capture_time)
                        
                        return CaptureResult(
                            success=True,
                            timestamp=capture_time,
                            image_data=image_data,
                            file_path=file_path,
                            image_size=len(image_data)
                        )
                    else:
                        return CaptureResult(
                            success=False,
                            timestamp=capture_time,
                            error_message="未接收到图片数据"
                        )
                else:
                    error_msg = f"抓拍指令发送失败，状态码: {response.status}"
                    logger.error(error_msg)
                    return CaptureResult(
                        success=False,
                        timestamp=capture_time,
                        error_message=error_msg
                    )
                    
        except Exception as e:
            error_msg = f"抓拍过程中发生错误: {e}"
            logger.error(error_msg)
            return CaptureResult(
                success=False,
                timestamp=capture_time,
                error_message=error_msg
            )
    
    async def _save_image(self, image_data: bytes, timestamp: datetime) -> str:
        """保存图片"""
        try:
            # 生成文件名
            filename = f"capture_{timestamp.strftime('%Y%m%d_%H%M%S_%f')}.jpg"
            file_path = Path(self.config.save_directory) / filename
            
            # 保存图片
            with open(file_path, 'wb') as f:
                f.write(image_data)
            
            logger.info(f"图片已保存到: {file_path} (大小: {len(image_data)/1024/1024:.2f} MB)")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"保存图片时发生错误: {e}")
            return ""
    
    async def get_camera_status(self) -> Dict[str, Any]:
        """获取相机状态"""
        try:
            url = f"http://{self.config.ip}:{self.config.port}/LAPI/V1.0/System/Status"
            headers = self.authenticator.get_auth_headers()
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    status_data = await response.json()
                    return {
                        "online": True,
                        "status": "正常",
                        "details": status_data
                    }
                else:
                    return {
                        "online": False,
                        "status": f"HTTP错误: {response.status}",
                        "details": {}
                    }
                    
        except Exception as e:
            return {
                "online": False,
                "status": f"连接错误: {e}",
                "details": {}
            }
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 停止图片接收
            self.image_receiver.stop_listening()
            
            # 关闭HTTP会话
            if self.session:
                await self.session.close()
                
            logger.info("系统资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源时发生错误: {e}")

# 使用示例
async def main():
    """主函数示例"""
    # 配置相机参数
    config = CameraConfig(
        ip="**************",
        port=4080,
        username="admin",
        password="ZZ@jtzf2526618"
    )
    
    # 创建抓拍系统
    capture_system = MinimalCameraSystem(config)
    
    try:
        # 初始化系统
        if not await capture_system.initialize():
            logger.error("系统初始化失败")
            return
        
        # 获取相机状态
        status = await capture_system.get_camera_status()
        logger.info(f"相机状态: {status}")
        
        # 执行单次抓拍
        logger.info("执行单次抓拍...")
        result = await capture_system.trigger_capture()
        
        if result.success:
            logger.info(f"抓拍成功！")
            logger.info(f"时间: {result.timestamp}")
            logger.info(f"文件: {result.file_path}")
            logger.info(f"大小: {result.image_size/1024/1024:.2f} MB")
        else:
            logger.error(f"抓拍失败: {result.error_message}")
        
    finally:
        # 清理资源
        await capture_system.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
