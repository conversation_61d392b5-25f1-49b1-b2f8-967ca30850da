#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相机抓拍系统工具模块
"""

import os
import shutil
import hashlib
import json
import time
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont

logger = logging.getLogger(__name__)

class ImageProcessor:
    """图像处理工具"""
    
    @staticmethod
    def validate_image(image_data: bytes) -> bool:
        """验证图像数据是否有效"""
        try:
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            return image is not None
        except Exception:
            return False
    
    @staticmethod
    def get_image_info(image_data: bytes) -> Dict[str, Any]:
        """获取图像信息"""
        try:
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                return {"valid": False}
            
            height, width, channels = image.shape
            
            return {
                "valid": True,
                "width": width,
                "height": height,
                "channels": channels,
                "size_bytes": len(image_data),
                "format": "JPEG" if image_data.startswith(b'\xff\xd8') else "Unknown"
            }
        except Exception as e:
            logger.error(f"获取图像信息失败: {e}")
            return {"valid": False, "error": str(e)}
    
    @staticmethod
    def resize_image(image_data: bytes, max_width: int = 1920, max_height: int = 1080) -> bytes:
        """调整图像大小"""
        try:
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                return image_data
            
            height, width = image.shape[:2]
            
            # 计算缩放比例
            scale_w = max_width / width
            scale_h = max_height / height
            scale = min(scale_w, scale_h, 1.0)  # 不放大
            
            if scale < 1.0:
                new_width = int(width * scale)
                new_height = int(height * scale)
                resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
                
                # 编码回JPEG
                _, encoded = cv2.imencode('.jpg', resized, [cv2.IMWRITE_JPEG_QUALITY, 85])
                return encoded.tobytes()
            
            return image_data
            
        except Exception as e:
            logger.error(f"调整图像大小失败: {e}")
            return image_data
    
    @staticmethod
    def add_watermark(image_data: bytes, text: str, position: str = "bottom_right") -> bytes:
        """添加水印"""
        try:
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                return image_data
            
            # 转换为PIL图像以便添加中文文字
            image_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(image_pil)
            
            # 尝试加载字体
            try:
                font = ImageFont.truetype("arial.ttf", 24)
            except:
                font = ImageFont.load_default()
            
            # 获取文字大小
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 计算位置
            img_width, img_height = image_pil.size
            margin = 10
            
            if position == "bottom_right":
                x = img_width - text_width - margin
                y = img_height - text_height - margin
            elif position == "bottom_left":
                x = margin
                y = img_height - text_height - margin
            elif position == "top_right":
                x = img_width - text_width - margin
                y = margin
            else:  # top_left
                x = margin
                y = margin
            
            # 添加半透明背景
            overlay = Image.new('RGBA', image_pil.size, (255, 255, 255, 0))
            overlay_draw = ImageDraw.Draw(overlay)
            overlay_draw.rectangle([x-5, y-5, x+text_width+5, y+text_height+5], 
                                 fill=(0, 0, 0, 128))
            
            # 合并背景
            image_pil = Image.alpha_composite(image_pil.convert('RGBA'), overlay)
            
            # 添加文字
            draw = ImageDraw.Draw(image_pil)
            draw.text((x, y), text, font=font, fill=(255, 255, 255, 255))
            
            # 转换回OpenCV格式
            image_cv = cv2.cvtColor(np.array(image_pil.convert('RGB')), cv2.COLOR_RGB2BGR)
            
            # 编码为JPEG
            _, encoded = cv2.imencode('.jpg', image_cv, [cv2.IMWRITE_JPEG_QUALITY, 85])
            return encoded.tobytes()
            
        except Exception as e:
            logger.error(f"添加水印失败: {e}")
            return image_data

class FileManager:
    """文件管理工具"""
    
    @staticmethod
    def ensure_directory(path: str) -> bool:
        """确保目录存在"""
        try:
            Path(path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"创建目录失败 {path}: {e}")
            return False
    
    @staticmethod
    def get_file_hash(file_path: str, algorithm: str = "md5") -> Optional[str]:
        """计算文件哈希值"""
        try:
            hash_func = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return None
    
    @staticmethod
    def get_directory_size(path: str) -> int:
        """获取目录大小（字节）"""
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
            return total_size
        except Exception as e:
            logger.error(f"获取目录大小失败 {path}: {e}")
            return 0
    
    @staticmethod
    def cleanup_old_files(directory: str, days: int = 30) -> int:
        """清理旧文件"""
        try:
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            deleted_count = 0
            
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.getmtime(file_path) < cutoff_time:
                        try:
                            os.remove(file_path)
                            deleted_count += 1
                            logger.debug(f"删除旧文件: {file_path}")
                        except Exception as e:
                            logger.warning(f"删除文件失败 {file_path}: {e}")
            
            logger.info(f"清理完成，删除了 {deleted_count} 个文件")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")
            return 0
    
    @staticmethod
    def get_disk_usage(path: str) -> Dict[str, float]:
        """获取磁盘使用情况"""
        try:
            total, used, free = shutil.disk_usage(path)
            return {
                "total_gb": total / (1024**3),
                "used_gb": used / (1024**3),
                "free_gb": free / (1024**3),
                "usage_percent": (used / total) * 100
            }
        except Exception as e:
            logger.error(f"获取磁盘使用情况失败: {e}")
            return {"total_gb": 0, "used_gb": 0, "free_gb": 0, "usage_percent": 0}

class DataExporter:
    """数据导出工具"""
    
    @staticmethod
    def export_to_json(data: List[Dict[str, Any]], file_path: str) -> bool:
        """导出为JSON格式"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            logger.info(f"数据已导出到: {file_path}")
            return True
        except Exception as e:
            logger.error(f"导出JSON失败: {e}")
            return False
    
    @staticmethod
    def export_to_csv(data: List[Dict[str, Any]], file_path: str) -> bool:
        """导出为CSV格式"""
        try:
            import csv
            
            if not data:
                return False
            
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
            
            logger.info(f"数据已导出到: {file_path}")
            return True
        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            return False

class NetworkUtils:
    """网络工具"""
    
    @staticmethod
    async def check_host_reachable(host: str, port: int, timeout: float = 5.0) -> bool:
        """检查主机是否可达"""
        try:
            future = asyncio.open_connection(host, port)
            reader, writer = await asyncio.wait_for(future, timeout=timeout)
            writer.close()
            await writer.wait_closed()
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_local_ip() -> str:
        """获取本地IP地址"""
        try:
            import socket
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            return "127.0.0.1"

class PerformanceMonitor:
    """性能监控工具"""
    
    def __init__(self):
        self.start_time = time.time()
        self.operation_times = []
    
    def start_operation(self) -> float:
        """开始操作计时"""
        return time.time()
    
    def end_operation(self, start_time: float, operation_name: str = ""):
        """结束操作计时"""
        duration = time.time() - start_time
        self.operation_times.append({
            "operation": operation_name,
            "duration": duration,
            "timestamp": datetime.now()
        })
        return duration
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self.operation_times:
            return {"total_operations": 0}
        
        durations = [op["duration"] for op in self.operation_times]
        
        return {
            "total_operations": len(self.operation_times),
            "total_time": sum(durations),
            "average_time": sum(durations) / len(durations),
            "min_time": min(durations),
            "max_time": max(durations),
            "uptime": time.time() - self.start_time
        }

# 工具函数
def format_timestamp(timestamp: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化时间戳"""
    return timestamp.strftime(format_str)

def parse_timestamp(timestamp_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
    """解析时间戳字符串"""
    return datetime.strptime(timestamp_str, format_str)

def generate_unique_filename(prefix: str = "capture", extension: str = "jpg") -> str:
    """生成唯一文件名"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
    return f"{prefix}_{timestamp}.{extension}"

def bytes_to_human_readable(bytes_size: int) -> str:
    """将字节数转换为人类可读格式"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_size < 1024.0:
            return f"{bytes_size:.2f} {unit}"
        bytes_size /= 1024.0
    return f"{bytes_size:.2f} PB"
