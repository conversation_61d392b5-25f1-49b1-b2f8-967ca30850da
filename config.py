#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相机抓拍系统配置文件
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class CameraConfig:
    """相机配置"""
    # 网络配置
    ip: str = "**********"
    port: int = 80
    rtsp_port: int = 554
    data_port: int = 10008
    client_port: int = 20002
    
    # 认证配置
    username: str = "admin"
    password: str = "admin123"
    
    # 超时配置
    timeout: int = 30
    connect_timeout: int = 10
    read_timeout: int = 20
    
    # 抓拍配置
    trigger_type: str = "IA_TRIGGER_FORMAL"
    image_format: str = "JPEG"
    image_quality: int = 85
    
    # 保存配置
    save_directory: str = "captured_images"
    save_original: bool = True
    save_processed: bool = True
    
    # 车牌识别配置
    enable_plate_recognition: bool = True
    plate_confidence_threshold: float = 0.7
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "camera_capture.log"

@dataclass
class SystemConfig:
    """系统配置"""
    # 并发配置
    max_concurrent_captures: int = 5
    max_retry_attempts: int = 3
    retry_delay: float = 1.0
    
    # 监控配置
    health_check_interval: int = 60
    keep_alive_interval: int = 30
    
    # 存储配置
    max_storage_days: int = 30
    auto_cleanup: bool = True
    max_disk_usage_percent: float = 80.0
    
    # 性能配置
    image_buffer_size: int = 10
    processing_threads: int = 4

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "camera_config.json"):
        self.config_file = Path(config_file)
        self.camera_config = CameraConfig()
        self.system_config = SystemConfig()
        
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if not self.config_file.exists():
                logger.info(f"配置文件 {self.config_file} 不存在，使用默认配置")
                self.save_config()
                return True
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 加载相机配置
            if 'camera' in config_data:
                camera_data = config_data['camera']
                for key, value in camera_data.items():
                    if hasattr(self.camera_config, key):
                        setattr(self.camera_config, key, value)
            
            # 加载系统配置
            if 'system' in config_data:
                system_data = config_data['system']
                for key, value in system_data.items():
                    if hasattr(self.system_config, key):
                        setattr(self.system_config, key, value)
            
            logger.info(f"配置文件 {self.config_file} 加载成功")
            return True
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            config_data = {
                'camera': asdict(self.camera_config),
                'system': asdict(self.system_config)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
            
            logger.info(f"配置文件 {self.config_file} 保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def update_camera_config(self, **kwargs) -> bool:
        """更新相机配置"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.camera_config, key):
                    setattr(self.camera_config, key, value)
                    logger.info(f"更新相机配置: {key} = {value}")
                else:
                    logger.warning(f"未知的相机配置项: {key}")
            
            return self.save_config()
            
        except Exception as e:
            logger.error(f"更新相机配置失败: {e}")
            return False
    
    def update_system_config(self, **kwargs) -> bool:
        """更新系统配置"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.system_config, key):
                    setattr(self.system_config, key, value)
                    logger.info(f"更新系统配置: {key} = {value}")
                else:
                    logger.warning(f"未知的系统配置项: {key}")
            
            return self.save_config()
            
        except Exception as e:
            logger.error(f"更新系统配置失败: {e}")
            return False
    
    def get_camera_config(self) -> CameraConfig:
        """获取相机配置"""
        return self.camera_config
    
    def get_system_config(self) -> SystemConfig:
        """获取系统配置"""
        return self.system_config
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""
        issues = []
        
        # 验证相机配置
        camera = self.camera_config
        
        # 检查IP地址格式
        try:
            import ipaddress
            ipaddress.ip_address(camera.ip)
        except ValueError:
            issues.append(f"无效的IP地址: {camera.ip}")
        
        # 检查端口范围
        if not (1 <= camera.port <= 65535):
            issues.append(f"无效的端口号: {camera.port}")
        
        if not (1 <= camera.client_port <= 65535):
            issues.append(f"无效的客户端端口号: {camera.client_port}")
        
        # 检查超时设置
        if camera.timeout <= 0:
            issues.append(f"超时时间必须大于0: {camera.timeout}")
        
        # 检查保存目录
        save_dir = Path(camera.save_directory)
        try:
            save_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            issues.append(f"无法创建保存目录 {save_dir}: {e}")
        
        # 验证系统配置
        system = self.system_config
        
        if system.max_concurrent_captures <= 0:
            issues.append(f"最大并发数必须大于0: {system.max_concurrent_captures}")
        
        if not (0 < system.max_disk_usage_percent <= 100):
            issues.append(f"磁盘使用率阈值必须在0-100之间: {system.max_disk_usage_percent}")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues
        }

# 全局配置管理器实例
config_manager = ConfigManager()

def get_camera_config() -> CameraConfig:
    """获取相机配置的便捷函数"""
    return config_manager.get_camera_config()

def get_system_config() -> SystemConfig:
    """获取系统配置的便捷函数"""
    return config_manager.get_system_config()

def load_config() -> bool:
    """加载配置的便捷函数"""
    return config_manager.load_config()

def save_config() -> bool:
    """保存配置的便捷函数"""
    return config_manager.save_config()

# 示例配置文件内容
EXAMPLE_CONFIG = {
    "camera": {
        "ip": "**********",
        "port": 80,
        "username": "admin",
        "password": "admin123",
        "timeout": 30,
        "trigger_type": "IA_TRIGGER_FORMAL",
        "save_directory": "captured_images",
        "enable_plate_recognition": True
    },
    "system": {
        "max_concurrent_captures": 5,
        "health_check_interval": 60,
        "auto_cleanup": True,
        "max_storage_days": 30
    }
}

if __name__ == "__main__":
    # 测试配置管理器
    config_manager = ConfigManager("test_config.json")
    
    # 加载配置
    config_manager.load_config()
    
    # 验证配置
    validation = config_manager.validate_config()
    print(f"配置验证结果: {validation}")
    
    # 显示当前配置
    print(f"相机配置: {config_manager.get_camera_config()}")
    print(f"系统配置: {config_manager.get_system_config()}")
