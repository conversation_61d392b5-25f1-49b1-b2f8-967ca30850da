# 相机抓拍系统

基于日志分析实现的智能相机抓拍系统，支持图片获取、车牌识别和数据管理。

## 功能特性

### 核心功能
- 🎯 **智能抓拍**: 支持单次、批量、连续抓拍模式
- 📸 **图片获取**: 自动接收和保存高质量图片
- 🚗 **车牌识别**: 内置车牌检测和识别功能
- 🌐 **多接口支持**: 命令行、Web界面、API接口
- ⚡ **异步处理**: 高性能异步架构

### 网络通信
- 🔐 **安全认证**: 支持用户名密码认证
- 🔄 **连接保活**: 自动维护连接状态
- 📡 **实时传输**: TCP数据流接收
- 🛡️ **错误重试**: 智能重连机制

### 数据管理
- 💾 **自动存储**: 图片自动分类保存
- 📊 **数据导出**: 支持JSON、CSV格式
- 🧹 **自动清理**: 定期清理过期文件
- 📈 **统计分析**: 抓拍成功率统计

## 系统架构

基于日志文件 `2.csv` 的分析，系统实现了以下通信流程：

```
客户端(**********) ←→ 相机设备(**********)
     ↓
1. HTTP认证 (端口80)
2. 抓拍指令 (PUT /LAPI/V1.0/Smart/CaptureExecution)
3. 图片数据传输 (TCP 10008→20002)
4. 保活维护 (PUT /LAPI/V1.0/System/Security/KeepAlive)
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd camera-capture-system

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置系统

```bash
# 生成默认配置文件
python config.py

# 编辑配置文件
vim camera_config.json
```

配置示例：
```json
{
    "camera": {
        "ip": "**********",
        "port": 80,
        "username": "admin",
        "password": "admin123",
        "save_directory": "captured_images"
    },
    "system": {
        "max_concurrent_captures": 5,
        "auto_cleanup": true,
        "max_storage_days": 30
    }
}
```

### 3. 使用方式

#### 命令行接口

```bash
# 检查系统状态
python cli.py status

# 单次抓拍
python cli.py capture

# 批量抓拍
python cli.py capture -c 5 -i 2.0

# 导出结果
python cli.py capture -c 3 -e json
```

#### Web界面

```bash
# 启动Web服务
python web_interface.py

# 访问 http://localhost:8000
```

#### Python API

```python
import asyncio
from camera_capture_system import CameraCaptureSystem
from config import CameraConfig

async def main():
    # 创建配置
    config = CameraConfig(
        ip="**********",
        username="admin",
        password="admin123"
    )
    
    # 初始化系统
    system = CameraCaptureSystem(config)
    await system.initialize()
    
    # 执行抓拍
    result = await system.trigger_capture()
    
    if result.success:
        print(f"抓拍成功: {result.file_path}")
        if result.plate_info:
            print(f"车牌号: {result.plate_info['plate_number']}")
    
    # 清理资源
    await system.cleanup()

asyncio.run(main())
```

## API接口

### HTTP API

| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/status` | GET | 获取系统状态 |
| `/api/capture` | POST | 触发抓拍 |
| `/api/config` | GET/POST | 配置管理 |
| `/api/images/{filename}` | GET | 获取图片 |

### WebSocket

- `/ws` - 实时状态推送和日志

## 配置说明

### 相机配置 (camera)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| ip | string | "**********" | 相机IP地址 |
| port | int | 80 | HTTP端口 |
| username | string | "admin" | 用户名 |
| password | string | "admin123" | 密码 |
| client_port | int | 20002 | 客户端监听端口 |
| timeout | int | 30 | 超时时间(秒) |
| save_directory | string | "captured_images" | 保存目录 |

### 系统配置 (system)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| max_concurrent_captures | int | 5 | 最大并发抓拍数 |
| health_check_interval | int | 60 | 健康检查间隔(秒) |
| auto_cleanup | bool | true | 自动清理过期文件 |
| max_storage_days | int | 30 | 文件保存天数 |

## 日志分析结果

基于提供的日志文件 `2.csv`，系统识别出以下关键信息：

### 通信协议
- **认证接口**: `PUT /LAPI/V1.0/System/Security/KeepAlive`
- **抓拍接口**: `PUT /LAPI/V1.0/Smart/CaptureExecution?IA_TRIGGER_FORMAL`
- **视频流**: `OPTIONS rtsp://**********/media/video4`

### 数据传输
- **图片大小**: 约1.98MB (1,983,646字节)
- **传输端口**: 10008(服务端) → 20002(客户端)
- **传输协议**: TCP分片传输
- **传输时间**: 约0.3秒

### 认证流程
1. 首次请求返回 `401 UnAuthorized`
2. 重新认证后成功执行
3. 定期发送保活请求

## 故障排除

### 常见问题

1. **连接失败**
   ```bash
   # 检查网络连通性
   ping **********
   
   # 检查端口开放
   telnet ********** 80
   ```

2. **认证失败**
   - 检查用户名密码是否正确
   - 确认相机设备支持的认证方式

3. **图片接收失败**
   - 检查客户端端口20002是否被占用
   - 确认防火墙设置

4. **车牌识别不准确**
   - 调整图片质量设置
   - 检查光照条件
   - 更新识别算法

### 日志查看

```bash
# 查看系统日志
tail -f camera_capture.log

# 查看详细调试信息
python cli.py status -v
```

## 开发指南

### 项目结构

```
camera-capture-system/
├── camera_capture_system.py  # 核心抓拍系统
├── config.py                 # 配置管理
├── utils.py                  # 工具函数
├── cli.py                    # 命令行接口
├── web_interface.py          # Web界面
├── requirements.txt          # 依赖包
├── README.md                 # 说明文档
└── captured_images/          # 图片保存目录
```

### 扩展开发

1. **添加新的抓拍模式**
   ```python
   async def custom_capture_mode(self):
       # 实现自定义抓拍逻辑
       pass
   ```

2. **集成其他识别算法**
   ```python
   class CustomPlateRecognizer:
       def recognize_plate(self, image_data):
           # 实现自定义识别算法
           pass
   ```

3. **添加数据库支持**
   ```python
   # 在utils.py中添加数据库操作
   class DatabaseManager:
       async def save_capture_result(self, result):
           pass
   ```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 联系方式

如有问题请联系开发团队。
