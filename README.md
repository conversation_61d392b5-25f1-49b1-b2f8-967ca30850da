# 相机抓拍系统

基于日志分析实现的智能相机抓拍系统，支持图片获取、车牌识别和数据管理。

## 🚀 快速启动

### 1. 安装依赖

```bash
# 安装Python依赖包
pip install -r requirements.txt
```

### 2. 启动系统

#### 方式一：Web界面（推荐新手）

```bash
# 启动Web服务器
python web_interface.py

# 浏览器访问: http://localhost:8000
```

#### 方式二：命令行（推荐开发者）

```bash
# 检查系统状态
python cli.py status

# 单次抓拍
python cli.py capture

# 批量抓拍（5次，间隔2秒）
python cli.py capture -c 5 -i 2.0

# 查看所有命令
python cli.py --help
```

#### 方式三：Python脚本

创建 `test_capture.py` 文件：
```python
import asyncio
from camera_capture_system import CameraCaptureSystem
from config import get_camera_config

async def main():
    # 获取配置
    config = get_camera_config()

    # 创建抓拍系统
    system = CameraCaptureSystem(config)

    try:
        # 初始化系统
        if await system.initialize():
            print("系统初始化成功")

            # 执行抓拍
            result = await system.trigger_capture()

            if result.success:
                print(f"✅ 抓拍成功!")
                print(f"📁 保存路径: {result.file_path}")
                if result.plate_info:
                    print(f"🚗 车牌号: {result.plate_info['plate_number']}")
            else:
                print(f"❌ 抓拍失败: {result.error_message}")
        else:
            print("❌ 系统初始化失败")

    finally:
        # 清理资源
        await system.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

然后运行：
```bash
python test_capture.py
```

## ⚙️ 系统配置

系统会自动生成配置文件 `camera_config.json`，基于日志分析的默认配置：

```json
{
    "camera": {
        "ip": "**************",
        "port": 4080,
        "username": "admin",
        "password": "ZZ@jtzf2526618",
        "client_port": 20002,
        "save_directory": "captured_images",
        "timeout": 30
    },
    "system": {
        "max_concurrent_captures": 5,
        "auto_cleanup": true,
        "max_storage_days": 30
    }
}
```

### 修改配置

```bash
# 查看当前配置
python cli.py config show

# 修改相机IP
python cli.py config set --key ip --value "*************"

# 修改用户名
python cli.py config set --key username --value "your_username"

# 验证配置
python cli.py config validate
```

## API接口

### HTTP API

| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/status` | GET | 获取系统状态 |
| `/api/capture` | POST | 触发抓拍 |
| `/api/config` | GET/POST | 配置管理 |
| `/api/images/{filename}` | GET | 获取图片 |

### WebSocket

- `/ws` - 实时状态推送和日志

## 配置说明

### 相机配置 (camera)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| ip | string | "**************" | 相机IP地址 |
| port | int | 80 | HTTP端口 |
| username | string | "admin" | 用户名 |
| password | string | "ZZ@jtzf2526618" | 密码 |
| client_port | int | 20002 | 客户端监听端口 |
| timeout | int | 30 | 超时时间(秒) |
| save_directory | string | "captured_images" | 保存目录 |

### 系统配置 (system)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| max_concurrent_captures | int | 5 | 最大并发抓拍数 |
| health_check_interval | int | 60 | 健康检查间隔(秒) |
| auto_cleanup | bool | true | 自动清理过期文件 |
| max_storage_days | int | 30 | 文件保存天数 |

## 日志分析结果

基于提供的日志文件 `2.csv`，系统识别出以下关键信息：

### 通信协议
- **认证接口**: `PUT /LAPI/V1.0/System/Security/KeepAlive`
- **抓拍接口**: `PUT /LAPI/V1.0/Smart/CaptureExecution?IA_TRIGGER_FORMAL`
- **视频流**: `OPTIONS rtsp://**************/media/video4`

### 数据传输
- **图片大小**: 约1.98MB (1,983,646字节)
- **传输端口**: 10008(服务端) → 20002(客户端)
- **传输协议**: TCP分片传输
- **传输时间**: 约0.3秒

### 认证流程
1. 首次请求返回 `401 UnAuthorized`
2. 重新认证后成功执行
3. 定期发送保活请求

## 故障排除

### 常见问题

1. **连接失败**
   ```bash
   # 检查网络连通性
   ping **************
   
   # 检查端口开放
   telnet ************** 80
   ```

2. **认证失败**
   - 检查用户名密码是否正确
   - 确认相机设备支持的认证方式

3. **图片接收失败**
   - 检查客户端端口20002是否被占用
   - 确认防火墙设置

4. **车牌识别不准确**
   - 调整图片质量设置
   - 检查光照条件
   - 更新识别算法

### 日志查看

```bash
# 查看系统日志
tail -f camera_capture.log

# 查看详细调试信息
python cli.py status -v
```

## 开发指南

### 项目结构

```
camera-capture-system/
├── camera_capture_system.py  # 核心抓拍系统
├── config.py                 # 配置管理
├── utils.py                  # 工具函数
├── cli.py                    # 命令行接口
├── web_interface.py          # Web界面
├── requirements.txt          # 依赖包
├── README.md                 # 说明文档
└── captured_images/          # 图片保存目录
```

### 扩展开发

1. **添加新的抓拍模式**
   ```python
   async def custom_capture_mode(self):
       # 实现自定义抓拍逻辑
       pass
   ```

2. **集成其他识别算法**
   ```python
   class CustomPlateRecognizer:
       def recognize_plate(self, image_data):
           # 实现自定义识别算法
           pass
   ```

3. **添加数据库支持**
   ```python
   # 在utils.py中添加数据库操作
   class DatabaseManager:
       async def save_capture_result(self, result):
           pass
   ```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 联系方式

如有问题请联系开发团队。
