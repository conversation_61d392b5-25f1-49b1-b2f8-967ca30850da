#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相机抓拍系统启动脚本
"""

import sys
import os
import asyncio
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'aiohttp', 'fastapi', 'uvicorn', 'opencv-python', 
        'Pillow', 'numpy', 'pydantic'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def setup_directories():
    """创建必要的目录"""
    print("📁 创建目录结构...")
    
    directories = [
        "captured_images",
        "logs",
        "static"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"  ✅ {directory}/")

def check_config():
    """检查配置文件"""
    print("⚙️  检查配置文件...")
    
    config_file = Path("camera_config.json")
    
    if not config_file.exists():
        print("  📝 生成默认配置文件...")
        try:
            from config import config_manager
            config_manager.save_config()
            print("  ✅ 配置文件已生成: camera_config.json")
        except Exception as e:
            print(f"  ❌ 生成配置文件失败: {e}")
            return False
    else:
        print("  ✅ 配置文件已存在")
    
    # 验证配置
    try:
        from config import config_manager
        config_manager.load_config()
        validation = config_manager.validate_config()
        
        if validation["valid"]:
            print("  ✅ 配置验证通过")
        else:
            print("  ⚠️  配置验证失败:")
            for issue in validation["issues"]:
                print(f"    - {issue}")
            return False
    except Exception as e:
        print(f"  ❌ 配置验证失败: {e}")
        return False
    
    return True

async def test_camera_connection():
    """测试相机连接"""
    print("🔗 测试相机连接...")
    
    try:
        from camera_capture_system import CameraCaptureSystem
        from config import get_camera_config
        
        config = get_camera_config()
        print(f"  📡 连接到相机: {config.ip}:{config.port}")
        
        system = CameraCaptureSystem(config)
        
        # 只测试初始化，不执行完整的抓拍
        if await system.initialize():
            print("  ✅ 相机连接成功")
            
            # 获取状态
            status = await system.get_camera_status()
            if status["online"]:
                print("  ✅ 相机状态正常")
            else:
                print(f"  ⚠️  相机状态异常: {status['status']}")
            
            await system.cleanup()
            return True
        else:
            print("  ❌ 相机连接失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 连接测试失败: {e}")
        return False

def show_startup_menu():
    """显示启动菜单"""
    print("\n" + "="*50)
    print("🎯 相机抓拍系统")
    print("="*50)
    print("请选择启动方式:")
    print("1. 🌐 Web界面 (推荐)")
    print("2. 💻 命令行模式")
    print("3. 🧪 测试抓拍")
    print("4. ⚙️  配置管理")
    print("5. 📊 系统状态")
    print("0. 退出")
    print("="*50)

def start_web_interface():
    """启动Web界面"""
    print("🌐 启动Web界面...")
    print("📍 访问地址: http://localhost:8000")
    print("按 Ctrl+C 停止服务")
    
    try:
        import uvicorn
        uvicorn.run(
            "web_interface:app",
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Web服务已停止")
    except Exception as e:
        print(f"❌ Web服务启动失败: {e}")

def start_cli_mode():
    """启动命令行模式"""
    print("💻 命令行模式")
    print("可用命令:")
    print("  python cli.py status     - 检查状态")
    print("  python cli.py capture    - 单次抓拍")
    print("  python cli.py capture -c 5 -i 2  - 批量抓拍")
    print("  python cli.py --help     - 查看帮助")
    
    while True:
        try:
            cmd = input("\n💻 请输入命令 (或输入 'exit' 退出): ").strip()
            
            if cmd.lower() in ['exit', 'quit', 'q']:
                break
            
            if cmd.startswith('python cli.py'):
                cmd = cmd.replace('python cli.py', 'cli.py')
            
            if cmd:
                # 执行命令
                result = subprocess.run([sys.executable, 'cli.py'] + cmd.split()[1:], 
                                      capture_output=True, text=True)
                
                if result.stdout:
                    print(result.stdout)
                if result.stderr:
                    print(f"错误: {result.stderr}")
        
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"命令执行失败: {e}")

async def test_capture():
    """测试抓拍功能"""
    print("🧪 测试抓拍功能...")
    
    try:
        from camera_capture_system import CameraCaptureSystem
        from config import get_camera_config
        
        config = get_camera_config()
        system = CameraCaptureSystem(config)
        
        if await system.initialize():
            print("📸 执行测试抓拍...")
            result = await system.trigger_capture()
            
            if result.success:
                print("✅ 抓拍测试成功!")
                print(f"📁 文件保存: {result.file_path}")
                if result.plate_info:
                    print(f"🚗 车牌信息: {result.plate_info}")
            else:
                print(f"❌ 抓拍测试失败: {result.error_message}")
            
            await system.cleanup()
        else:
            print("❌ 系统初始化失败")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def config_management():
    """配置管理"""
    print("⚙️  配置管理")
    
    while True:
        print("\n配置选项:")
        print("1. 查看当前配置")
        print("2. 修改相机IP")
        print("3. 修改用户名密码")
        print("4. 验证配置")
        print("0. 返回主菜单")
        
        choice = input("请选择: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            subprocess.run([sys.executable, 'cli.py', 'config', 'show'])
        elif choice == '2':
            ip = input("请输入新的相机IP: ").strip()
            if ip:
                subprocess.run([sys.executable, 'cli.py', 'config', 'set', '--key', 'ip', '--value', ip])
        elif choice == '3':
            username = input("请输入用户名: ").strip()
            password = input("请输入密码: ").strip()
            if username:
                subprocess.run([sys.executable, 'cli.py', 'config', 'set', '--key', 'username', '--value', username])
            if password:
                subprocess.run([sys.executable, 'cli.py', 'config', 'set', '--key', 'password', '--value', password])
        elif choice == '4':
            subprocess.run([sys.executable, 'cli.py', 'config', 'validate'])

async def main():
    """主函数"""
    print("🚀 相机抓拍系统启动中...")
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 创建目录
    setup_directories()
    
    # 检查配置
    if not check_config():
        print("❌ 配置检查失败，请检查配置文件")
        return
    
    # 测试连接
    connection_ok = await test_camera_connection()
    if not connection_ok:
        print("⚠️  相机连接测试失败，但您仍可以继续使用系统")
    
    # 显示菜单
    while True:
        show_startup_menu()
        
        try:
            choice = input("请选择 (0-5): ").strip()
            
            if choice == '0':
                print("👋 再见!")
                break
            elif choice == '1':
                start_web_interface()
            elif choice == '2':
                start_cli_mode()
            elif choice == '3':
                await test_capture()
            elif choice == '4':
                config_management()
            elif choice == '5':
                subprocess.run([sys.executable, 'cli.py', 'status', '-v'])
            else:
                print("❌ 无效选择，请重新输入")
        
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请检查Python环境和依赖包是否正确安装")
