#!/bin/bash

# 相机抓拍系统启动脚本

echo "========================================"
echo "🎯 相机抓拍系统启动脚本"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到Python3，请先安装Python 3.7+"
    exit 1
fi

echo "✅ Python环境检查通过"

# 检查依赖包
echo
echo "🔍 检查依赖包..."
python3 -c "import aiohttp, fastapi, cv2, PIL" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  缺少依赖包，正在安装..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ 依赖包安装失败"
        exit 1
    fi
else
    echo "✅ 依赖包检查通过"
fi

echo
echo "🚀 启动系统..."
python3 start.py
