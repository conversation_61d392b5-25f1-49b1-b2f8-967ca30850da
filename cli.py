#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相机抓拍系统命令行接口
"""

import asyncio
import argparse
import sys
import json
from pathlib import Path
from datetime import datetime
import logging

from camera_capture_system import CameraCaptureSystem, CaptureResult
from config import ConfigManager, CameraConfig
from utils import (
    ImageProcessor, FileManager, DataExporter, 
    NetworkUtils, PerformanceMonitor, format_timestamp
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CameraCLI:
    """相机抓拍系统命令行接口"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.capture_system = None
        self.performance_monitor = PerformanceMonitor()
    
    async def initialize(self) -> bool:
        """初始化系统"""
        try:
            # 加载配置
            if not self.config_manager.load_config():
                logger.error("配置加载失败")
                return False
            
            # 验证配置
            validation = self.config_manager.validate_config()
            if not validation["valid"]:
                logger.error(f"配置验证失败: {validation['issues']}")
                return False
            
            # 创建抓拍系统
            camera_config = self.config_manager.get_camera_config()
            self.capture_system = CameraCaptureSystem(camera_config)
            
            # 初始化抓拍系统
            if not await self.capture_system.initialize():
                logger.error("抓拍系统初始化失败")
                return False
            
            logger.info("系统初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.capture_system:
            await self.capture_system.cleanup()
    
    async def cmd_capture(self, args) -> int:
        """执行抓拍命令"""
        try:
            if not await self.initialize():
                return 1
            
            start_time = self.performance_monitor.start_operation()
            
            if args.count > 1:
                # 批量抓拍
                logger.info(f"开始批量抓拍，数量: {args.count}, 间隔: {args.interval}秒")
                results = await self.capture_system.batch_capture(
                    count=args.count, 
                    interval=args.interval
                )
            else:
                # 单次抓拍
                logger.info("开始单次抓拍")
                result = await self.capture_system.trigger_capture()
                results = [result]
            
            duration = self.performance_monitor.end_operation(start_time, "capture")
            
            # 统计结果
            success_count = sum(1 for r in results if r.success)
            
            print(f"\n抓拍完成:")
            print(f"  总数: {len(results)}")
            print(f"  成功: {success_count}")
            print(f"  失败: {len(results) - success_count}")
            print(f"  耗时: {duration:.2f}秒")
            
            # 显示详细结果
            if args.verbose:
                for i, result in enumerate(results, 1):
                    print(f"\n第{i}次抓拍:")
                    print(f"  状态: {'成功' if result.success else '失败'}")
                    print(f"  时间: {format_timestamp(result.timestamp)}")
                    if result.success:
                        print(f"  文件: {result.file_path}")
                        if result.plate_info:
                            print(f"  车牌: {result.plate_info.get('plate_number', 'N/A')}")
                    else:
                        print(f"  错误: {result.error_message}")
            
            # 导出结果
            if args.export:
                await self._export_results(results, args.export)
            
            return 0 if success_count > 0 else 1
            
        except Exception as e:
            logger.error(f"抓拍命令执行失败: {e}")
            return 1
        finally:
            await self.cleanup()
    
    async def cmd_status(self, args) -> int:
        """检查系统状态"""
        try:
            if not await self.initialize():
                return 1
            
            # 获取相机状态
            camera_status = await self.capture_system.get_camera_status()
            
            # 获取网络状态
            camera_config = self.config_manager.get_camera_config()
            network_ok = await NetworkUtils.check_host_reachable(
                camera_config.ip, camera_config.port
            )
            
            # 获取存储状态
            disk_usage = FileManager.get_disk_usage(camera_config.save_directory)
            
            print("系统状态:")
            print(f"  相机连接: {'正常' if camera_status['online'] else '异常'}")
            print(f"  网络连接: {'正常' if network_ok else '异常'}")
            print(f"  存储空间: {disk_usage['free_gb']:.2f}GB 可用 ({disk_usage['usage_percent']:.1f}% 已使用)")
            
            if args.verbose:
                print(f"\n详细信息:")
                print(f"  相机IP: {camera_config.ip}:{camera_config.port}")
                print(f"  保存目录: {camera_config.save_directory}")
                print(f"  相机状态: {camera_status}")
            
            return 0 if camera_status['online'] and network_ok else 1
            
        except Exception as e:
            logger.error(f"状态检查失败: {e}")
            return 1
        finally:
            await self.cleanup()
    
    async def cmd_config(self, args) -> int:
        """配置管理命令"""
        try:
            if args.action == "show":
                # 显示当前配置
                camera_config = self.config_manager.get_camera_config()
                system_config = self.config_manager.get_system_config()
                
                print("当前配置:")
                print("\n相机配置:")
                for key, value in camera_config.__dict__.items():
                    if key != "password":  # 隐藏密码
                        print(f"  {key}: {value}")
                
                print("\n系统配置:")
                for key, value in system_config.__dict__.items():
                    print(f"  {key}: {value}")
            
            elif args.action == "set":
                # 设置配置项
                if not args.key or not args.value:
                    print("错误: 需要指定 --key 和 --value 参数")
                    return 1
                
                # 尝试更新相机配置
                if self.config_manager.update_camera_config(**{args.key: args.value}):
                    print(f"相机配置已更新: {args.key} = {args.value}")
                # 尝试更新系统配置
                elif self.config_manager.update_system_config(**{args.key: args.value}):
                    print(f"系统配置已更新: {args.key} = {args.value}")
                else:
                    print(f"错误: 未知的配置项 {args.key}")
                    return 1
            
            elif args.action == "validate":
                # 验证配置
                validation = self.config_manager.validate_config()
                if validation["valid"]:
                    print("配置验证通过")
                else:
                    print("配置验证失败:")
                    for issue in validation["issues"]:
                        print(f"  - {issue}")
                    return 1
            
            return 0
            
        except Exception as e:
            logger.error(f"配置命令执行失败: {e}")
            return 1
    
    async def cmd_cleanup(self, args) -> int:
        """清理命令"""
        try:
            camera_config = self.config_manager.get_camera_config()
            
            if args.days:
                # 清理旧文件
                deleted_count = FileManager.cleanup_old_files(
                    camera_config.save_directory, args.days
                )
                print(f"清理完成，删除了 {deleted_count} 个文件")
            
            if args.disk_check:
                # 检查磁盘使用情况
                disk_usage = FileManager.get_disk_usage(camera_config.save_directory)
                print(f"磁盘使用情况:")
                print(f"  总空间: {disk_usage['total_gb']:.2f}GB")
                print(f"  已使用: {disk_usage['used_gb']:.2f}GB ({disk_usage['usage_percent']:.1f}%)")
                print(f"  可用空间: {disk_usage['free_gb']:.2f}GB")
            
            return 0
            
        except Exception as e:
            logger.error(f"清理命令执行失败: {e}")
            return 1
    
    async def _export_results(self, results: list, export_format: str):
        """导出结果"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 准备导出数据
            export_data = []
            for result in results:
                data = {
                    "timestamp": format_timestamp(result.timestamp),
                    "success": result.success,
                    "file_path": result.file_path,
                    "error_message": result.error_message
                }
                
                if result.plate_info:
                    data.update(result.plate_info)
                
                export_data.append(data)
            
            # 导出文件
            if export_format.lower() == "json":
                file_path = f"capture_results_{timestamp}.json"
                DataExporter.export_to_json(export_data, file_path)
            elif export_format.lower() == "csv":
                file_path = f"capture_results_{timestamp}.csv"
                DataExporter.export_to_csv(export_data, file_path)
            else:
                logger.warning(f"不支持的导出格式: {export_format}")
                return
            
            print(f"结果已导出到: {file_path}")
            
        except Exception as e:
            logger.error(f"导出结果失败: {e}")

def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="相机抓拍系统命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 抓拍命令
    capture_parser = subparsers.add_parser("capture", help="执行抓拍")
    capture_parser.add_argument("-c", "--count", type=int, default=1, help="抓拍次数")
    capture_parser.add_argument("-i", "--interval", type=float, default=1.0, help="抓拍间隔(秒)")
    capture_parser.add_argument("-v", "--verbose", action="store_true", help="显示详细信息")
    capture_parser.add_argument("-e", "--export", choices=["json", "csv"], help="导出结果格式")
    
    # 状态命令
    status_parser = subparsers.add_parser("status", help="检查系统状态")
    status_parser.add_argument("-v", "--verbose", action="store_true", help="显示详细信息")
    
    # 配置命令
    config_parser = subparsers.add_parser("config", help="配置管理")
    config_parser.add_argument("action", choices=["show", "set", "validate"], help="配置操作")
    config_parser.add_argument("--key", help="配置项名称")
    config_parser.add_argument("--value", help="配置项值")
    
    # 清理命令
    cleanup_parser = subparsers.add_parser("cleanup", help="清理数据")
    cleanup_parser.add_argument("--days", type=int, help="清理N天前的文件")
    cleanup_parser.add_argument("--disk-check", action="store_true", help="检查磁盘使用情况")
    
    return parser

async def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    cli = CameraCLI()
    
    try:
        if args.command == "capture":
            return await cli.cmd_capture(args)
        elif args.command == "status":
            return await cli.cmd_status(args)
        elif args.command == "config":
            return await cli.cmd_config(args)
        elif args.command == "cleanup":
            return await cli.cmd_cleanup(args)
        else:
            print(f"未知命令: {args.command}")
            return 1
    
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return 1
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
