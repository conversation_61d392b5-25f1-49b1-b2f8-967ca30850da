#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相机抓拍系统测试脚本
"""

import asyncio
import sys
from datetime import datetime
from pathlib import Path

# 导入系统模块
try:
    from camera_capture_system import CameraCaptureSystem
    from config import get_camera_config, config_manager
    from utils import format_timestamp, NetworkUtils
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保所有文件都在同一目录下")
    sys.exit(1)

async def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始基本功能测试...")
    
    # 1. 测试配置加载
    print("\n1️⃣ 测试配置加载...")
    try:
        config_manager.load_config()
        config = get_camera_config()
        print(f"  ✅ 配置加载成功")
        print(f"  📡 相机地址: {config.ip}:{config.port}")
        print(f"  👤 用户名: {config.username}")
        print(f"  📁 保存目录: {config.save_directory}")
    except Exception as e:
        print(f"  ❌ 配置加载失败: {e}")
        return False
    
    # 2. 测试网络连接
    print("\n2️⃣ 测试网络连接...")
    try:
        is_reachable = await NetworkUtils.check_host_reachable(config.ip, config.port, timeout=5.0)
        if is_reachable:
            print(f"  ✅ 网络连接正常")
        else:
            print(f"  ⚠️  网络连接失败，但可能是相机未开启或网络配置问题")
    except Exception as e:
        print(f"  ❌ 网络测试失败: {e}")
    
    # 3. 测试系统初始化
    print("\n3️⃣ 测试系统初始化...")
    system = None
    try:
        system = CameraCaptureSystem(config)
        if await system.initialize():
            print("  ✅ 系统初始化成功")
        else:
            print("  ❌ 系统初始化失败")
            return False
    except Exception as e:
        print(f"  ❌ 系统初始化异常: {e}")
        return False
    
    # 4. 测试相机状态
    print("\n4️⃣ 测试相机状态...")
    try:
        status = await system.get_camera_status()
        if status["online"]:
            print("  ✅ 相机状态正常")
            print(f"  📊 状态详情: {status['status']}")
        else:
            print(f"  ⚠️  相机状态异常: {status['status']}")
    except Exception as e:
        print(f"  ❌ 状态检查失败: {e}")
    
    # 5. 测试抓拍功能
    print("\n5️⃣ 测试抓拍功能...")
    try:
        print("  📸 执行测试抓拍...")
        result = await system.trigger_capture()
        
        if result.success:
            print("  ✅ 抓拍成功!")
            print(f"  ⏰ 抓拍时间: {format_timestamp(result.timestamp)}")
            print(f"  📁 保存路径: {result.file_path}")
            
            # 检查文件是否存在
            if result.file_path and Path(result.file_path).exists():
                file_size = Path(result.file_path).stat().st_size
                print(f"  📏 文件大小: {file_size} 字节")
            
            # 检查车牌信息
            if result.plate_info:
                print(f"  🚗 车牌信息: {result.plate_info}")
            else:
                print("  ℹ️  未检测到车牌信息")
        else:
            print(f"  ❌ 抓拍失败: {result.error_message}")
    except Exception as e:
        print(f"  ❌ 抓拍测试异常: {e}")
    
    # 6. 清理资源
    print("\n6️⃣ 清理系统资源...")
    try:
        await system.cleanup()
        print("  ✅ 资源清理完成")
    except Exception as e:
        print(f"  ⚠️  资源清理异常: {e}")
    
    return True

async def test_batch_capture():
    """测试批量抓拍"""
    print("\n🔄 测试批量抓拍功能...")
    
    try:
        config = get_camera_config()
        system = CameraCaptureSystem(config)
        
        if await system.initialize():
            print("  📸 执行批量抓拍 (3次，间隔1秒)...")
            results = await system.batch_capture(count=3, interval=1.0)
            
            success_count = sum(1 for r in results if r.success)
            print(f"  📊 批量抓拍结果: {success_count}/{len(results)} 成功")
            
            for i, result in enumerate(results, 1):
                status = "✅" if result.success else "❌"
                print(f"    {status} 第{i}次: {format_timestamp(result.timestamp)}")
                if result.success and result.file_path:
                    print(f"      📁 {result.file_path}")
        
        await system.cleanup()
        
    except Exception as e:
        print(f"  ❌ 批量抓拍测试失败: {e}")

async def test_performance():
    """测试性能"""
    print("\n⚡ 性能测试...")
    
    try:
        config = get_camera_config()
        system = CameraCaptureSystem(config)
        
        if await system.initialize():
            # 测试单次抓拍性能
            start_time = asyncio.get_event_loop().time()
            result = await system.trigger_capture()
            end_time = asyncio.get_event_loop().time()
            
            duration = end_time - start_time
            print(f"  ⏱️  单次抓拍耗时: {duration:.2f} 秒")
            
            if result.success:
                print("  ✅ 性能测试通过")
            else:
                print("  ⚠️  抓拍失败，无法评估性能")
        
        await system.cleanup()
        
    except Exception as e:
        print(f"  ❌ 性能测试失败: {e}")

def show_test_summary():
    """显示测试总结"""
    print("\n" + "="*60)
    print("📋 测试总结")
    print("="*60)
    print("如果所有测试都通过，说明系统运行正常。")
    print("如果有测试失败，请检查:")
    print("  1. 相机设备是否开启并连接到网络")
    print("  2. IP地址和端口配置是否正确")
    print("  3. 用户名密码是否正确")
    print("  4. 网络防火墙设置")
    print("  5. 客户端端口是否被占用")
    print("\n启动系统:")
    print("  python start.py        # 交互式启动")
    print("  python web_interface.py # 直接启动Web界面")
    print("  python cli.py status   # 命令行检查状态")
    print("="*60)

async def main():
    """主测试函数"""
    print("🎯 相机抓拍系统测试")
    print("="*40)
    
    # 基本功能测试
    basic_ok = await test_basic_functionality()
    
    if basic_ok:
        # 如果基本功能正常，继续其他测试
        print("\n" + "="*40)
        
        # 询问是否进行更多测试
        try:
            choice = input("是否进行批量抓拍测试? (y/N): ").strip().lower()
            if choice in ['y', 'yes']:
                await test_batch_capture()
            
            choice = input("是否进行性能测试? (y/N): ").strip().lower()
            if choice in ['y', 'yes']:
                await test_performance()
        
        except KeyboardInterrupt:
            print("\n测试被用户中断")
    
    # 显示总结
    show_test_summary()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试已退出")
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        print("请检查Python环境和依赖包")
