# 🚀 相机抓拍系统 - 快速启动指南

## 📋 系统要求

- Python 3.7+ 
- Windows 10+ 或 Linux
- 网络连接到相机设备

## ⚡ 一键启动

### Windows用户
```bash
# 双击运行
start.bat

# 或在命令行中运行
start.bat
```

### Linux/Mac用户
```bash
# 在终端中运行
./start.sh

# 或者
bash start.sh
```

### 通用方式
```bash
# 安装依赖
pip install -r requirements.txt

# 启动系统
python start.py
```

## 🎯 启动选项

启动后会看到菜单：

```
🎯 相机抓拍系统
==================================================
请选择启动方式:
1. 🌐 Web界面 (推荐)
2. 💻 命令行模式  
3. 🧪 测试抓拍
4. ⚙️  配置管理
5. 📊 系统状态
0. 退出
==================================================
```

### 1. Web界面（推荐新手）
- 选择 `1` 启动Web界面
- 浏览器访问: http://localhost:8000
- 图形化操作，简单易用

### 2. 命令行模式（推荐开发者）
- 选择 `2` 进入命令行模式
- 支持所有高级功能
- 适合脚本化操作

### 3. 测试抓拍
- 选择 `3` 快速测试系统
- 验证相机连接和抓拍功能

## 🔧 配置说明

系统会自动生成配置文件 `camera_config.json`：

```json
{
    "camera": {
        "ip": "**********",        // 相机IP地址
        "port": 80,                // HTTP端口
        "username": "admin",       // 用户名
        "password": "admin123",    // 密码
        "client_port": 20002,      // 客户端监听端口
        "save_directory": "captured_images"  // 图片保存目录
    }
}
```

### 修改配置
1. 启动系统选择 `4. 配置管理`
2. 或直接编辑 `camera_config.json` 文件
3. 或使用命令行: `python cli.py config set --key ip --value "新IP"`

## 📸 使用示例

### Web界面操作
1. 启动Web界面
2. 检查系统状态
3. 设置抓拍参数（次数、间隔）
4. 点击"开始抓拍"
5. 查看抓拍结果和图片

### 命令行操作
```bash
# 检查状态
python cli.py status

# 单次抓拍  
python cli.py capture

# 批量抓拍（5次，间隔2秒）
python cli.py capture -c 5 -i 2

# 导出结果为JSON
python cli.py capture -c 3 -e json
```

### Python脚本
```python
import asyncio
from camera_capture_system import CameraCaptureSystem
from config import get_camera_config

async def main():
    config = get_camera_config()
    system = CameraCaptureSystem(config)
    
    if await system.initialize():
        result = await system.trigger_capture()
        if result.success:
            print(f"抓拍成功: {result.file_path}")
    
    await system.cleanup()

asyncio.run(main())
```

## 🔍 故障排除

### 常见问题

1. **依赖包安装失败**
   ```bash
   # 升级pip
   python -m pip install --upgrade pip
   
   # 使用国内镜像
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

2. **相机连接失败**
   - 检查IP地址是否正确
   - 确认相机设备已开启
   - 检查网络连通性: `ping **********`

3. **端口被占用**
   ```bash
   # Windows查看端口占用
   netstat -ano | findstr :20002
   
   # Linux查看端口占用  
   netstat -tulpn | grep :20002
   ```

4. **权限问题**
   - 确保有文件写入权限
   - Windows可能需要以管理员身份运行

### 测试连接
```bash
# 运行测试脚本
python test_capture.py

# 或在启动菜单选择 "3. 测试抓拍"
```

## 📁 文件结构

```
camera-capture-system/
├── start.py                  # 主启动脚本
├── start.bat                 # Windows启动脚本
├── start.sh                  # Linux启动脚本
├── test_capture.py           # 测试脚本
├── camera_capture_system.py  # 核心系统
├── config.py                 # 配置管理
├── cli.py                    # 命令行接口
├── web_interface.py          # Web界面
├── utils.py                  # 工具函数
├── requirements.txt          # 依赖包
├── camera_config.json        # 配置文件（自动生成）
└── captured_images/          # 图片保存目录
```

## 🆘 获取帮助

1. **查看命令帮助**
   ```bash
   python cli.py --help
   python cli.py capture --help
   ```

2. **运行测试**
   ```bash
   python test_capture.py
   ```

3. **检查日志**
   - 查看控制台输出
   - 检查 `logs/` 目录下的日志文件

## 🎉 开始使用

现在您可以开始使用相机抓拍系统了！

推荐流程：
1. 运行 `start.bat` (Windows) 或 `./start.sh` (Linux)
2. 选择 `3. 测试抓拍` 验证系统
3. 选择 `1. Web界面` 开始正式使用

祝您使用愉快！ 🎯
