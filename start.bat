@echo off
chcp 65001 >nul
title 相机抓拍系统

echo.
echo ========================================
echo 🎯 相机抓拍系统启动脚本
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

REM 检查依赖包
echo.
echo 🔍 检查依赖包...
python -c "import aiohttp, fastapi, cv2, PIL" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少依赖包，正在安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 依赖包检查通过
)

echo.
echo 🚀 启动系统...
python start.py

pause
